# 字符集冲突解决方案

## 问题描述
错误信息：`SQLSTATE[HY000]: General error: 1267 Illegal mix of collations (utf8_general_ci,IMPLICIT) and (utf8mb4_general_ci,COERCIBLE) for operation '='`

这个错误是由于数据库表字段使用了不同的字符集排序规则导致的。

## 已实施的临时解决方案

已修改 `application/api/controller/Isv.php` 文件中的三个查询方法：

1. `saveAuthInfo` 方法（第268行）
2. `refreshAppAuthToken` 方法（第329行）  
3. `checkAuthStatus` 方法（第481行）

修改内容：将原来的 `where('account_identity', $value)` 改为 `whereRaw('BINARY account_identity = BINARY ?', [$value])`

这样可以避免字符集冲突，立即解决当前问题。

## 长期解决方案

### 步骤1：检查当前状态
执行以下SQL查看 account 表的字符集设置：

```sql
-- 检查表的字符集
SELECT 
    TABLE_NAME,
    TABLE_COLLATION,
    TABLE_SCHEMA
FROM information_schema.TABLES 
WHERE TABLE_NAME = 'fa_account' 
AND TABLE_SCHEMA = DATABASE();

-- 检查字段的字符集
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 'fa_account' 
AND TABLE_SCHEMA = DATABASE()
AND CHARACTER_SET_NAME IS NOT NULL;
```

### 步骤2：备份数据
在执行任何修改前，请务必备份数据库：

```bash
mysqldump -u用户名 -p密码 数据库名 > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 步骤3：修复字符集
执行 `fix_charset_conflict.sql` 文件中的SQL语句，将 account 表统一改为 utf8mb4 字符集。

### 步骤4：验证修复结果
重新执行步骤1的查询，确认所有字段都使用 utf8mb4 字符集。

### 步骤5：回滚临时修改（可选）
如果长期解决方案生效，可以将代码中的 `whereRaw` 改回原来的 `where` 方法。

## 注意事项

1. **数据备份**：执行任何数据库结构修改前必须备份
2. **测试环境**：建议先在测试环境验证修改效果
3. **业务影响**：字符集转换可能需要一定时间，建议在业务低峰期执行
4. **兼容性**：utf8mb4 向下兼容 utf8，不会影响现有数据

## 预防措施

1. 在创建新表时统一使用 utf8mb4 字符集
2. 定期检查数据库字符集一致性
3. 在开发环境保持与生产环境相同的字符集设置
