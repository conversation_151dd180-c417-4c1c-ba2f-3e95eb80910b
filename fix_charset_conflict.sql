-- 修复字符集冲突问题的SQL脚本
-- 使用前请先备份数据库！

-- 1. 检查当前 account 表的字符集和排序规则
SELECT 
    TABLE_NAME,
    TABLE_COLLATION,
    TABLE_SCHEMA
FROM information_schema.TABLES 
WHERE TABLE_NAME = 'fa_account' 
AND TABLE_SCHEMA = DATABASE();

-- 2. 检查 account 表中各字段的字符集和排序规则
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 'fa_account' 
AND TABLE_SCHEMA = DATABASE()
AND CHARACTER_SET_NAME IS NOT NULL;

-- 3. 修复方案：将 account 表及其字段统一改为 utf8mb4 字符集
-- 注意：执行前请确保已备份数据！

-- 修改表的默认字符集
ALTER TABLE fa_account CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 如果上面的命令不够，可以单独修改特定字段
-- ALTER TABLE fa_account MODIFY COLUMN account_identity VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- ALTER TABLE fa_account MODIFY COLUMN config TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 4. 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 'fa_account' 
AND TABLE_SCHEMA = DATABASE()
AND CHARACTER_SET_NAME IS NOT NULL;
